# 本地文件夹Fallback机制需求文档

## 功能概述

在MapHandler方法中实现本地文件夹fallback机制，当向keyValue数据库请求地图瓦片失败或需要更新时，在NATS请求和在线provider逻辑之前，根据provider类型自动尝试对应的本地文件夹版本provider类型，从本地文件系统获取地图瓦片。

## 背景分析

### 现有Provider类型

根据`proto/db.proto`中的`MapProviderEnum`定义，系统支持以下6种provider类型：

1. **在线Provider类型**：
   - `ProviderGoogle` (0) - Google地图在线服务
   - `ProviderTianditu` (1) - 天地图在线服务
   - `ProviderOSM` (2) - OpenStreetMap在线服务

2. **本地文件夹Provider类型**：
   - `ProviderGoogleLocalDirectory` (3) - Google地图本地文件夹
   - `ProviderTiandituLocalDirectory` (4) - 天地图本地文件夹
   - `ProviderOSMLocalDirectory` (5) - OSM本地文件夹

### 当前实现分析

从`maps/maps.go`的`MapHttpHandler`方法分析，当前的处理流程为：

1. 首先从keyValue数据库查询瓦片 (`QueryTileFromKeyValueDb`)
2. 如果数据库中没有或需要更新，通过NATS请求其他节点
3. 最后通过`QueryTileFromProvider`从在线服务获取瓦片

**本地文件夹fallback的插入点**：需要在步骤1和步骤2之间插入本地文件夹fallback逻辑，即当缓存获取失败或需要更新时，先尝试本地文件夹，然后再执行NATS和在线provider流程。

目前在`QueryTileFromProvider`方法中，只处理了在线provider类型（Google、Tianditu、OSM），没有处理本地文件夹类型。

## 具体需求

### 1. Provider映射关系

建立在线provider与本地文件夹provider的对应关系：

| 在线Provider | 本地文件夹Provider | 数值映射 |
|-------------|------------------|---------|
| ProviderGoogle (0) | ProviderGoogleLocalDirectory (3) | 0 → 3 |
| ProviderTianditu (1) | ProviderTiandituLocalDirectory (4) | 1 → 4 |
| ProviderOSM (2) | ProviderOSMLocalDirectory (5) | 2 → 5 |

### 2. Fallback触发条件

本地文件夹fallback机制作为现有逻辑流程的补充，在以下情况下触发：

- `QueryTileFromKeyValueDb`返回错误（瓦片不存在于数据库）
- 或者`QueryTileFromKeyValueDb`返回的瓦片需要更新（基于缓存时间和Status判断）

**重要说明**：本地文件夹fallback不会替换现有的NATS请求和在线provider逻辑，而是在这些步骤之前，作为缓存获取失败或需要更新的额外的fallback选项。当从缓存无法获取到正确可用的瓦片时，系统会尝试从本地文件夹获取瓦片，然后再继续现有的NATS请求和在线provider逻辑。

### 3. 本地文件路径格式

根据`proto/db.proto`中的注释，本地文件路径格式为：

- **BaseUrl字段**：存储本地目录的根路径
- **文件路径格式**：`{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}`

具体格式：

- Google卫星图：`{BaseUrl}/satellite/{z}/{x}/{y}.jpg`
- Google混合图：`{BaseUrl}/hybrid/{z}/{x}/{y}.jpg`
- Google路线图：`{BaseUrl}/roadmap/{z}/{x}/{y}.png`
- 天地图和OSM：与Google相同的路径格式

### 4. 实现逻辑

#### 4.1 集成到现有流程

本地文件夹fallback机制将集成到`MapHttpHandler`的现有处理流程中，具体位置在：

1. **主流程集成点**：在`MapHttpHandler`的`sfg.Do`函数内部
2. **触发时机**：当`QueryTileFromKeyValueDb`返回错误或需要更新瓦片时
3. **执行顺序**：
   - 首先尝试本地文件夹fallback
   - 然后执行现有的NATS请求逻辑（如果启用）
   - 最后执行现有的`QueryTileFromProvider`在线请求逻辑

#### 4.2 本地文件夹fallback实现

当缓存获取失败或需要更新时，在NATS和在线请求之前，首先执行以下步骤：

1. 根据当前provider类型计算对应的本地文件夹provider类型
2. 查询该本地文件夹provider的token配置
3. 从本地文件系统读取瓦片文件
4. 如果成功读取，保存到数据库并返回
5. 如果本地文件夹fallback失败，继续执行现有的NATS和在线provider流程

#### 4.3 新增本地文件读取方法

创建新方法`QueryTileFromLocalDirectory`：

```go
func QueryTileFromLocalDirectory(
    userRid string,
    mapReq *MapReq,
    localProviderType int,
    needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error)
```

#### 4.4 本地文件夹Provider Token处理

- 扩展`ReqMapFromProviderWithToken`方法，支持本地文件夹provider类型
- 本地文件夹provider的token中，`BaseUrl`字段存储本地目录根路径
- 不需要网络请求，直接从文件系统读取

### 5. 错误处理

- 如果本地文件不存在，记录日志但不抛出错误
- 如果本地文件读取失败（权限、IO错误等），记录错误日志

### 6. 性能考虑

- 本地文件读取应该比网络请求更快
- 考虑添加本地文件缓存机制，避免重复读取同一文件
- 文件读取失败时，避免频繁重试

## 实现计划

### 阶段1：核心功能实现

1. 修改`MapHttpHandler`方法，在缓存查询失败后、NATS请求之前插入本地文件夹fallback逻辑 ✅
2. 实现`QueryTileFromLocalDirectory`方法 ✅
3. 扩展`ReqMapFromProviderWithToken`方法支持本地文件夹类型 ✅

### 阶段2：扩展应用场景

1. 重构`handleWithCacheOrOSMMapTile`方法，添加`enableLocalDirectoryFallback`参数控制本地文件夹fallback ✅
2. 修改`natsHandlerForMapReq`方法，在缓存查询失败后添加本地文件夹fallback ✅

### 阶段3：语言支持扩展

1. 为`DbMapProviderToken`添加`Language`字段支持多语言瓦片存储 ✅
2. 重构`QueryTileFromLocalDirectory`方法，添加语言匹配逻辑 ✅
3. 实现向后兼容性，支持没有设置language字段的旧token ✅

**应用场景说明**：

- `handleWithCacheOrOSMMapTile`：处理过期token请求，当缓存无法查到时需要本地文件夹fallback
  - **启用条件**：仅在`sysexpired=1`参数触发时启用本地文件夹fallback
  - **禁用条件**：临时token（`resolveTempMapProjectToken`调用）时不启用本地文件夹fallback
- `natsHandlerForMapReq`：处理NATS节点间的瓦片请求，当本节点缓存无法查到时需要本地文件夹fallback

**重构方案**：

1. **方法签名修改**：`handleWithCacheOrOSMMapTile(w, r, mapReq, enableLocalDirectoryFallback bool)`
2. **调用点修改**：
   - `sysexpired=1`场景：`handleWithCacheOrOSMMapTile(w, r, mapReq, true)` - 启用本地文件夹fallback
   - 临时token场景：`handleWithCacheOrOSMMapTile(w, r, mapReq, false)` - 禁用本地文件夹fallback

**语言支持方案**：

1. **数据库字段**：在`DbMapProviderToken`中添加`string Language = 20`字段
2. **Provider特定语言匹配规则**：
   - **Google本地文件夹**：支持多语言匹配，标准语言代码匹配
   - **天地图本地文件夹**：仅支持简体中文（zh-CN），其他语言不匹配
   - **OSM本地文件夹**：无语言选项，语言根据地理位置确定，跳过所有语言匹配
3. **语言代码标准化**：
   - 统一中文语言代码格式：`zh`、`zh_CN`、`zh-cn`、`zh_cn` → `zh-CN`
   - 确保匹配的准确性和一致性
4. **地图类型规则**：
   - roadmap和hybrid类型：应用provider特定的语言匹配规则
   - satellite类型：跳过语言匹配（卫星图无语言差别）
   - 空Language字段：作为默认语言，始终匹配（向后兼容）
5. **文件路径格式**：保持不变，通过不同token的BaseUrl区分语言版本

### 阶段4：集成测试

1. 创建测试用的本地文件夹结构
2. 配置本地文件夹provider token
3. 测试各种场景下的fallback机制

### 阶段4：优化和完善

1. 添加详细的日志记录
2. 性能优化
3. 错误处理完善

## 测试计划

### 1. 单元测试

- 测试provider类型映射逻辑
- 测试本地文件路径构建
- 测试文件读取功能

### 2. 集成测试

- 测试完整的fallback流程
- 测试不同mapType的文件格式
- 测试文件不存在的情况

### 3. 性能测试

- 对比网络请求和本地文件读取的性能
- 测试高并发情况下的表现

## 风险评估

### 技术风险

- 本地文件系统IO性能可能成为瓶颈
- 大量本地文件可能占用过多磁盘空间
- 文件权限问题可能导致读取失败

### 业务风险

- 本地文件可能不是最新版本
- 本地文件覆盖范围可能不完整
- 需要额外的文件管理和更新机制

## 后续扩展

1. **文件自动更新机制**：定期从在线服务更新本地文件
2. **智能缓存策略**：根据访问频率决定哪些瓦片需要本地存储
3. **文件压缩存储**：优化本地存储空间使用
4. **分布式文件存储**：支持多节点间的文件共享

## 总结

本需求旨在提高地图服务的可靠性和响应速度，通过本地文件夹fallback机制，在网络服务不可用时仍能提供地图瓦片服务。实现过程中需要注意性能优化和错误处理，确保系统的稳定性和用户体验。
