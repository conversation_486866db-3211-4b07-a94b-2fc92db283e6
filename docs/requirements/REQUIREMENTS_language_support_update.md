# 本地文件夹Provider语言支持功能更新

## 更新概述

根据本地文件夹provider的实际语言支持情况，对语言匹配逻辑进行了具体优化，实现了provider特定的语言支持规则。

## 更新内容

### 1. Provider特定语言支持规则

#### 1.1 Google本地文件夹 (ProviderGoogleLocalDirectory)
- **支持**：多语言匹配
- **匹配逻辑**：标准语言代码匹配
- **行为**：token.Language与mapReq.Lang进行标准化后精确匹配

#### 1.2 天地图本地文件夹 (ProviderTiandituLocalDirectory)  
- **支持**：仅支持简体中文
- **匹配逻辑**：只接受中文语言代码
- **行为**：
  - 如果token.Language为空：始终匹配（向后兼容）
  - 如果token.Language为"zh-CN"且mapReq.Lang也为"zh-CN"：匹配
  - 其他情况：不匹配

#### 1.3 OSM本地文件夹 (ProviderOSMLocalDirectory)
- **支持**：无语言选项
- **匹配逻辑**：跳过所有语言匹配
- **行为**：语言根据地理位置/国家确定，始终返回true

### 2. 语言代码标准化

#### 2.1 标准化函数
新增`normalizeLanguageCode`函数，统一语言代码格式：

```go
func normalizeLanguageCode(lang string) string {
    if lang == "" {
        return ""
    }
    
    // Normalize Chinese language codes
    switch lang {
    case "zh", "zh_CN", "zh-cn", "zh_cn":
        return "zh-CN"
    default:
        return lang
    }
}
```

#### 2.2 支持的中文格式
- `zh` → `zh-CN`
- `zh_CN` → `zh-CN`  
- `zh-cn` → `zh-CN`
- `zh_cn` → `zh-CN`

### 3. 更新的语言匹配逻辑

#### 3.1 卫星图类型
- **所有provider**：跳过语言匹配（卫星图无语言差别）

#### 3.2 路线图和混合图类型
- **Google**：标准多语言匹配
- **天地图**：仅中文匹配
- **OSM**：跳过语言匹配

#### 3.3 向后兼容性
- **空Language字段**：在所有provider中都作为默认/fallback，始终匹配
- **现有token**：不受影响，继续正常工作

## 实现细节

### 修改的文件
- `maps/maps.go`：更新`isLanguageMatched`函数，新增`normalizeLanguageCode`函数

### 核心逻辑变更
1. **Provider类型检查**：根据不同的本地文件夹provider类型应用不同规则
2. **语言标准化**：在匹配前对语言代码进行标准化处理
3. **特殊处理**：为天地图添加中文专用逻辑，为OSM跳过语言检查

## 测试场景

### 1. Google本地文件夹
- ✅ token.Language="en", mapReq.Lang="en" → 匹配
- ✅ token.Language="zh-CN", mapReq.Lang="zh" → 匹配（标准化后）
- ❌ token.Language="en", mapReq.Lang="zh-CN" → 不匹配

### 2. 天地图本地文件夹
- ✅ token.Language="", mapReq.Lang="zh-CN" → 匹配（空token）
- ✅ token.Language="zh-CN", mapReq.Lang="zh" → 匹配（标准化后）
- ❌ token.Language="zh-CN", mapReq.Lang="en" → 不匹配

### 3. OSM本地文件夹
- ✅ token.Language="", mapReq.Lang="en" → 匹配（跳过检查）
- ✅ token.Language="zh-CN", mapReq.Lang="en" → 匹配（跳过检查）
- ✅ token.Language="en", mapReq.Lang="zh-CN" → 匹配（跳过检查）

### 4. 卫星图类型
- ✅ 所有provider和语言组合 → 匹配（跳过语言检查）

## 优势

1. **精确匹配**：根据每个provider的实际语言支持能力进行匹配
2. **标准化处理**：统一语言代码格式，避免格式差异导致的匹配失败
3. **向后兼容**：保持现有token和配置的正常工作
4. **性能优化**：OSM provider跳过不必要的语言检查
5. **用户体验**：天地图用户无需担心语言设置，OSM用户获得地理位置相关的语言

## 注意事项

1. **天地图限制**：只支持中文，非中文请求将无法匹配天地图本地文件夹token
2. **OSM特性**：语言由地理位置决定，不受token语言设置影响
3. **Google灵活性**：支持多语言，需要精确的语言代码匹配
4. **标准化范围**：目前只对中文语言代码进行标准化，其他语言保持原样

## 后续扩展

1. **更多语言标准化**：可以扩展`normalizeLanguageCode`函数支持更多语言的格式统一
2. **地区代码支持**：可以考虑添加地区代码的标准化处理
3. **配置化规则**：可以将provider语言支持规则配置化，便于维护和扩展
