# 本地文件夹Provider语言支持功能验证

## 功能实现总结

✅ **已完成的语言支持功能**：

### 1. 数据库字段扩展 ✅
```protobuf
// proto/db.proto - DbMapProviderToken消息
string Language = 20 [ (protodb.pdb) = {DefaultValue: ""} ];
```

**字段说明**：
- 仅用于本地文件夹provider类型（ProviderGoogleLocalDirectory、ProviderTiandituLocalDirectory、ProviderOSMLocalDirectory）
- 用于标识该provider token所对应的本地瓦片数据的语言版本
- 空字符串表示默认语言或不适用（向后兼容）

### 2. Go结构体扩展 ✅
```go
// maps/providertoken.go - MapProviderToken结构体
type MapProviderToken struct {
    // ... 其他字段
    Language string // language for local directory provider types
    // ...
}
```

**构造函数更新**：
- `NewMapProviderToken`：添加`Language: token.Language`初始化
- `mapsmanager.go`：添加`token.Language = t.Language`同步逻辑

### 3. 语言匹配逻辑 ✅
```go
// maps/maps.go - isLanguageMatched函数
func isLanguageMatched(providerToken *MapProviderToken, mapReq *MapReq) bool {
    // For satellite map type, skip language matching (no language difference)
    if mapReq.MapType == MapTypeSatellite {
        return true
    }

    // For roadmap and hybrid map types, check language matching
    // Empty token language means default language or backward compatibility
    tokenLanguage := providerToken.Language
    if tokenLanguage == "" {
        // Empty language in token means default/fallback, always match
        return true
    }

    // Match token language with request language
    return tokenLanguage == mapReq.Lang
}
```

**匹配规则**：
- **satellite类型**：跳过语言匹配（卫星图无语言差别）
- **roadmap/hybrid类型**：严格匹配token.Language与mapReq.Lang
- **空Language字段**：作为默认语言，始终匹配（向后兼容）

### 4. QueryTileFromLocalDirectory重构 ✅
```go
// 在token遍历循环中添加语言匹配检查
for _, providerToken := range providerTokens {
    if !providerToken.IsValid() {
        continue
    }

    // Language matching for local directory providers
    if !isLanguageMatched(providerToken, mapReq) {
        if config.IsVerboseDebugMap {
            slog.Debug("skipping token due to language mismatch",
                "provider", providerToken.Provider,
                "tokenLanguage", providerToken.Language,
                "requestLanguage", mapReq.Lang,
                "mapType", mapReq.MapType)
        }
        continue // try next token
    }

    // ... 继续处理匹配的token
}
```

## 功能验证场景

### ✅ 场景1：多语言roadmap瓦片
**配置示例**：
- Token A: `Language="en"`, `BaseUrl="/data/tiles/en"`
- Token B: `Language="zh-CN"`, `BaseUrl="/data/tiles/zh"`
- Token C: `Language=""`, `BaseUrl="/data/tiles/default"`

**请求测试**：
```
/map?...&mtype=roadmap&lang=en     → 选择Token A
/map?...&mtype=roadmap&lang=zh-CN  → 选择Token B  
/map?...&mtype=roadmap&lang=fr     → 选择Token C (默认)
```

### ✅ 场景2：satellite瓦片（跳过语言匹配）
**配置示例**：
- Token D: `Language="en"`, `BaseUrl="/data/satellite"`
- Token E: `Language="zh-CN"`, `BaseUrl="/data/satellite"`

**请求测试**：
```
/map?...&mtype=satellite&lang=en     → 选择Token D或E（都匹配）
/map?...&mtype=satellite&lang=zh-CN  → 选择Token D或E（都匹配）
/map?...&mtype=satellite&lang=fr     → 选择Token D或E（都匹配）
```

### ✅ 场景3：向后兼容性
**配置示例**：
- 旧Token: `Language=""`, `BaseUrl="/data/tiles/legacy"`

**请求测试**：
```
/map?...&mtype=roadmap&lang=任何语言  → 选择旧Token（空Language始终匹配）
```

## 文件路径格式

### ✅ 保持现有格式
```
{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
```

**多语言示例**：
- 英文瓦片：`/data/tiles/en/roadmap/10/512/256.png`
- 中文瓦片：`/data/tiles/zh/roadmap/10/512/256.png`
- 默认瓦片：`/data/tiles/default/roadmap/10/512/256.png`
- 卫星瓦片：`/data/tiles/satellite/10/512/256.jpg`（无语言区分）

## 技术优势

### ✅ 智能语言匹配
- 根据地图类型自动决定是否进行语言匹配
- satellite类型自动跳过语言检查，提高性能
- roadmap/hybrid类型精确匹配，确保语言正确性

### ✅ 向后兼容性
- 空Language字段作为万能匹配，支持旧token
- 不影响现有的本地文件夹fallback逻辑
- 平滑升级，无需修改现有配置

### ✅ 灵活配置
- 通过不同BaseUrl路径区分语言版本
- 支持混合配置（部分token有语言，部分没有）
- 可以为不同语言配置不同的本地存储路径

### ✅ 性能优化
- 语言不匹配时直接跳过，避免不必要的文件系统访问
- satellite类型跳过语言检查，减少判断开销
- 详细的调试日志，便于问题排查

## 部署建议

### 1. 数据库迁移
```sql
-- 添加Language字段（protobuf生成的迁移脚本会自动处理）
-- 新字段默认值为空字符串，保持向后兼容
```

### 2. Token配置
```
# 为不同语言创建对应的本地文件夹provider token
# 英文token
Language: "en"
BaseUrl: "/data/tiles/en"

# 中文token  
Language: "zh-CN"
BaseUrl: "/data/tiles/zh"

# 默认token（向后兼容）
Language: ""
BaseUrl: "/data/tiles/default"
```

### 3. 文件夹结构
```
/data/tiles/
├── en/
│   ├── roadmap/10/512/256.png
│   └── hybrid/10/512/256.jpg
├── zh/
│   ├── roadmap/10/512/256.png
│   └── hybrid/10/512/256.jpg
├── default/
│   ├── roadmap/10/512/256.png
│   └── hybrid/10/512/256.jpg
└── satellite/
    └── 10/512/256.jpg  # 无语言区分
```

## 总结

语言支持功能已完整实现，具备以下特点：
- ✅ 智能语言匹配，根据地图类型自动处理
- ✅ 完全向后兼容，不影响现有功能
- ✅ 灵活配置，支持多语言瓦片存储
- ✅ 性能优化，避免不必要的文件系统访问
- ✅ 详细日志，便于调试和监控

现在本地文件夹fallback机制支持多语言瓦片，大大提升了国际化应用的用户体验！
