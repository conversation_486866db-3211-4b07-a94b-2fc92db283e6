// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file db.proto (package dbproto, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import { file_protodb } from "./protodb_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file db.proto.
 */
export const file_db: GenFile = /*@__PURE__*/
  fileDesc("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", [file_protodb]);

/**
 * DbOrg
 *
 * @generated from message dbproto.DbOrg
 */
export type DbOrg = Message<"dbproto.DbOrg"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * name
   *
   * @generated from field: string Name = 2;
   */
  Name: string;

  /**
   * create time
   *
   * @generated from field: int64 CreateTime = 3;
   */
  CreateTime: bigint;

  /**
   * note
   *
   * @generated from field: string Note = 4;
   */
  Note: string;

  /**
   * setting
   *
   * @generated from field: string Setting = 5;
   */
  Setting: string;

  /**
   * owner: user rid
   *
   * @generated from field: string OwnerRid = 6;
   */
  OwnerRid: string;
};

/**
 * Describes the message dbproto.DbOrg.
 * Use `create(DbOrgSchema)` to create a new message.
 */
export const DbOrgSchema: GenMessage<DbOrg> = /*@__PURE__*/
  messageDesc(file_db, 0);

/**
 * @generated from message dbproto.DbUser
 */
export type DbUser = Message<"dbproto.DbUser"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * org
   *
   * @generated from field: string Org = 2;
   */
  Org: string;

  /**
   * name
   *
   * @generated from field: string Name = 3;
   */
  Name: string;

  /**
   * nickname
   *
   * @generated from field: string Nickname = 4;
   */
  Nickname: string;

  /**
   * email
   *
   * @generated from field: string Email = 5;
   */
  Email: string;

  /**
   * phone
   *
   * @generated from field: string Phone = 6;
   */
  Phone: string;

  /**
   * password, base64(sha256(Name+Password))
   *
   * @generated from field: string Password = 7;
   */
  Password: string;

  /**
   * create time
   *
   * @generated from field: int64 CreateTime = 8;
   */
  CreateTime: bigint;

  /**
   * creater uuid
   *
   * @generated from field: string Creater = 9;
   */
  Creater: string;

  /**
   * note
   *
   * @generated from field: string Note = 10;
   */
  Note: string;

  /**
   * setting
   *
   * @generated from field: string Setting = 11;
   */
  Setting: string;

  /**
   * disabled
   *
   * @generated from field: bool Disabled = 12;
   */
  Disabled: boolean;
};

/**
 * Describes the message dbproto.DbUser.
 * Use `create(DbUserSchema)` to create a new message.
 */
export const DbUserSchema: GenMessage<DbUser> = /*@__PURE__*/
  messageDesc(file_db, 1);

/**
 * @generated from message dbproto.DbUserPrivilege
 */
export type DbUserPrivilege = Message<"dbproto.DbUserPrivilege"> & {
  /**
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: string UserRid = 2;
   */
  UserRid: string;

  /**
   * @generated from field: int64 UpdateTime = 3;
   */
  UpdateTime: bigint;

  /**
   * @generated from field: bool CanModifyOtherUser = 4;
   */
  CanModifyOtherUser: boolean;
};

/**
 * Describes the message dbproto.DbUserPrivilege.
 * Use `create(DbUserPrivilegeSchema)` to create a new message.
 */
export const DbUserPrivilegeSchema: GenMessage<DbUserPrivilege> = /*@__PURE__*/
  messageDesc(file_db, 2);

/**
 * user login session
 *
 * @generated from message dbproto.DbUserSession
 */
export type DbUserSession = Message<"dbproto.DbUserSession"> & {
  /**
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: string UserRid = 2;
   */
  UserRid: string;

  /**
   * @generated from field: string SessionId = 3;
   */
  SessionId: string;

  /**
   * @generated from field: int64 UpdateTime = 4;
   */
  UpdateTime: bigint;

  /**
   * @generated from field: int64 ExpireTime = 5;
   */
  ExpireTime: bigint;
};

/**
 * Describes the message dbproto.DbUserSession.
 * Use `create(DbUserSessionSchema)` to create a new message.
 */
export const DbUserSessionSchema: GenMessage<DbUserSession> = /*@__PURE__*/
  messageDesc(file_db, 3);

/**
 * user project
 *
 * @generated from message dbproto.DbProject
 */
export type DbProject = Message<"dbproto.DbProject"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * project user rid
   *
   * @generated from field: string UserRid = 2;
   */
  UserRid: string;

  /**
   * name
   *
   * @generated from field: string Name = 3;
   */
  Name: string;

  /**
   * create time
   *
   * @generated from field: int64 CreateTime = 4;
   */
  CreateTime: bigint;

  /**
   * note
   *
   * @generated from field: string Note = 5;
   */
  Note: string;

  /**
   * setting
   *
   * @generated from field: string Setting = 6;
   */
  Setting: string;

  /**
   * status, 1:active 4:disable 8:deleted
   *
   * @generated from field: int32 Status = 7;
   */
  Status: number;

  /**
   * for clean usage
   *
   * @generated from field: int64 DeleteTime = 8;
   */
  DeleteTime: bigint;
};

/**
 * Describes the message dbproto.DbProject.
 * Use `create(DbProjectSchema)` to create a new message.
 */
export const DbProjectSchema: GenMessage<DbProject> = /*@__PURE__*/
  messageDesc(file_db, 4);

/**
 * TAvailableMapProvider
 *
 * @generated from message dbproto.TAvailableMapProvider
 */
export type TAvailableMapProvider = Message<"dbproto.TAvailableMapProvider"> & {
  /**
   * 在http请求参数`provider`为空时使用的provider
   *
   * @generated from field: dbproto.MapProviderEnum DefaultRoadmap = 1;
   */
  DefaultRoadmap: MapProviderEnum;

  /**
   * @generated from field: dbproto.MapProviderEnum DefaultSatellite = 2;
   */
  DefaultSatellite: MapProviderEnum;

  /**
   * @generated from field: dbproto.MapProviderEnum DefaultHybrid = 3;
   */
  DefaultHybrid: MapProviderEnum;

  /**
   * google
   *
   * @generated from field: bool Google = 4;
   */
  Google: boolean;

  /**
   * tianditu
   *
   * @generated from field: bool Tianditu = 5;
   */
  Tianditu: boolean;

  /**
   * osm
   *
   * @generated from field: bool OSM = 6;
   */
  OSM: boolean;
};

/**
 * Describes the message dbproto.TAvailableMapProvider.
 * Use `create(TAvailableMapProviderSchema)` to create a new message.
 */
export const TAvailableMapProviderSchema: GenMessage<TAvailableMapProvider> = /*@__PURE__*/
  messageDesc(file_db, 5);

/**
 * @generated from message dbproto.SysNames
 */
export type SysNames = Message<"dbproto.SysNames"> & {
  /**
   * @generated from field: repeated string Names = 1;
   */
  Names: string[];
};

/**
 * Describes the message dbproto.SysNames.
 * Use `create(SysNamesSchema)` to create a new message.
 */
export const SysNamesSchema: GenMessage<SysNames> = /*@__PURE__*/
  messageDesc(file_db, 6);

/**
 * project token
 *
 * @generated from message dbproto.DbProjectToken
 */
export type DbProjectToken = Message<"dbproto.DbProjectToken"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * project rid
   *
   * @generated from field: string ProjectRid = 2;
   */
  ProjectRid: string;

  /**
   * @generated from field: string Name = 3;
   */
  Name: string;

  /**
   * token
   *
   * @generated from field: string Token = 4;
   */
  Token: string;

  /**
   * create time
   *
   * @generated from field: int64 CreateTime = 5;
   */
  CreateTime: bigint;

  /**
   * note
   *
   * @generated from field: string Note = 6;
   */
  Note: string;

  /**
   * setting
   *
   * @generated from field: string Setting = 7;
   */
  Setting: string;

  /**
   * expire time unix utc, 0:forever
   *
   * @generated from field: int64 ExpireTime = 8;
   */
  ExpireTime: bigint;

  /**
   * status, 1:active 4:disable, 8:deleted
   *
   * @generated from field: int32 Status = 9;
   */
  Status: number;

  /**
   * available map provider jsonb of TAvailableMapProvider
   *
   * @generated from field: string AvailableMapProvider = 10;
   */
  AvailableMapProvider: string;

  /**
   * for clean usage
   *
   * @generated from field: int64 DeleteTime = 11;
   */
  DeleteTime: bigint;

  /**
   * sys name, specify which system can access this token
   *
   * @generated from field: dbproto.SysNames SysName = 12;
   */
  SysName?: SysNames;
};

/**
 * Describes the message dbproto.DbProjectToken.
 * Use `create(DbProjectTokenSchema)` to create a new message.
 */
export const DbProjectTokenSchema: GenMessage<DbProjectToken> = /*@__PURE__*/
  messageDesc(file_db, 7);

/**
 * project quotas
 *
 * @generated from message dbproto.DbProjectQuotas
 */
export type DbProjectQuotas = Message<"dbproto.DbProjectQuotas"> & {
  /**
   * rid uuidv7 same as DbProject rid
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * quotas type 1:per minute 2:per hour 3:per day 4:per month
   *
   * @generated from field: int32 QuotasType = 2;
   */
  QuotasType: number;

  /**
   * quotas limit 0=unlimited
   *
   * @generated from field: int32 QuotasLimit = 3;
   */
  QuotasLimit: number;

  /**
   * count start time
   *
   * @generated from field: int64 CountStartTime = 4;
   */
  CountStartTime: bigint;

  /**
   * current quotas
   *
   * @generated from field: int32 CurrentUsedQuotas = 5;
   */
  CurrentUsedQuotas: number;
};

/**
 * Describes the message dbproto.DbProjectQuotas.
 * Use `create(DbProjectQuotasSchema)` to create a new message.
 */
export const DbProjectQuotasSchema: GenMessage<DbProjectQuotas> = /*@__PURE__*/
  messageDesc(file_db, 8);

/**
 * map project token usage statistics
 *
 * @generated from message dbproto.DbProjectTokenUsage
 */
export type DbProjectTokenUsage = Message<"dbproto.DbProjectTokenUsage"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * project rid
   *
   * @generated from field: string ProjectRid = 2;
   */
  ProjectRid: string;

  /**
   * token
   *
   * @generated from field: string Token = 3;
   */
  Token: string;

  /**
   * start time
   *
   * @generated from field: int64 StartTime = 4;
   */
  StartTime: bigint;

  /**
   * end time
   *
   * @generated from field: int64 EndTime = 5;
   */
  EndTime: bigint;

  /**
   * usage count, request success times
   *
   * @generated from field: int32 UsageCount = 6;
   */
  UsageCount: number;
};

/**
 * Describes the message dbproto.DbProjectTokenUsage.
 * Use `create(DbProjectTokenUsageSchema)` to create a new message.
 */
export const DbProjectTokenUsageSchema: GenMessage<DbProjectTokenUsage> = /*@__PURE__*/
  messageDesc(file_db, 9);

/**
 * map provider token
 *
 * @generated from message dbproto.DbMapProviderToken
 */
export type DbMapProviderToken = Message<"dbproto.DbMapProviderToken"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: dbproto.MapProviderEnum Provider = 2;
   */
  Provider: MapProviderEnum;

  /**
   * user rid
   *
   * @generated from field: string UserRid = 3;
   */
  UserRid: string;

  /**
   * @generated from field: string Name = 4;
   */
  Name: string;

  /**
   * token
   * for osm, it pass token in url as query string "tk=xxx"
   *
   * @generated from field: string Token = 5;
   */
  Token: string;

  /**
   * zoom level range, 0:no limit
   *
   * @generated from field: int32 MinZoom = 6;
   */
  MinZoom: number;

  /**
   * @generated from field: int32 MaxZoom = 7;
   */
  MaxZoom: number;

  /**
   * create time
   *
   * @generated from field: int64 CreateTime = 8;
   */
  CreateTime: bigint;

  /**
   * note
   *
   * @generated from field: string Note = 9;
   */
  Note: string;

  /**
   * expire time unix utc,0:forever
   *
   * @generated from field: int64 ExpireTime = 10;
   */
  ExpireTime: bigint;

  /**
   * setting
   *
   * @generated from field: string Setting = 11;
   */
  Setting: string;

  /**
   * quotas type  3:per day 4:per month
   *
   * @generated from field: int32 QuotasType = 12;
   */
  QuotasType: number;

  /**
   * quotas  0=unlimited
   *
   * @generated from field: int32 QuotasLimit = 13;
   */
  QuotasLimit: number;

  /**
   * status, 1:active 4:disable,8:deleted
   *
   * @generated from field: int32 Status = 14;
   */
  Status: number;

  /**
   * use which google map api to get tile
   *
   * @generated from field: dbproto.GoogleMapApiEnum GoogleMapApi = 15;
   */
  GoogleMapApi: GoogleMapApiEnum;

  /**
   * @generated from field: int32 Priority = 16;
   */
  Priority: number;

  /**
   * for clean usage
   *
   * @generated from field: int64 DeleteTime = 17;
   */
  DeleteTime: bigint;

  /**
   * custom base url in request
   * for google map static api, it replace the default base url "https://maps.googleapis.com"
   * for google map tile api, it replace the default base url "https://tile.googleapis.com"
   * for tianditu map api, it replace the default base url "https://t0.tianditu.com"
   * for osm map api, it replace the default base url "https://tile.openstreetmap.org"
   * for ProviderGoogleLocalDirectory, this is the local directory path, the file path is like "satellite/{z}/{x}/{y}.jpg"
   * or "hybrid/{z}/{x}/{y}.jpg" or "roadmap/{z}/{x}/{y}.png"
   * ProviderTiandituLocalDirectory and ProviderOSMLocalDirectory are the same as ProviderGoogleLocalDirectory
   *
   * @generated from field: string BaseUrl = 18;
   */
  BaseUrl: string;

  /**
   * @generated from field: bool IsUseProxy = 19;
   */
  IsUseProxy: boolean;

  /**
   * language for local directory provider types (ProviderGoogleLocalDirectory, ProviderTiandituLocalDirectory, ProviderOSMLocalDirectory)
   * used to identify the language version of local tile data
   * for roadmap and hybrid map types: match with mapReq.Lang
   * for satellite map type: ignored (no language difference)
   * empty string means default language or not applicable
   *
   * @generated from field: string Language = 20;
   */
  Language: string;
};

/**
 * Describes the message dbproto.DbMapProviderToken.
 * Use `create(DbMapProviderTokenSchema)` to create a new message.
 */
export const DbMapProviderTokenSchema: GenMessage<DbMapProviderToken> = /*@__PURE__*/
  messageDesc(file_db, 10);

/**
 * map provider used quotas
 *
 * @generated from message dbproto.DbMapProviderUsedQuotas
 */
export type DbMapProviderUsedQuotas = Message<"dbproto.DbMapProviderUsedQuotas"> & {
  /**
   * rid uuidv7 same as DbMapProviderToken rid
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * count start time
   *
   * @generated from field: int64 CountStartTime = 2;
   */
  CountStartTime: bigint;

  /**
   * current quotas
   *
   * @generated from field: int32 UsedQuotas = 3;
   */
  UsedQuotas: number;
};

/**
 * Describes the message dbproto.DbMapProviderUsedQuotas.
 * Use `create(DbMapProviderUsedQuotasSchema)` to create a new message.
 */
export const DbMapProviderUsedQuotasSchema: GenMessage<DbMapProviderUsedQuotas> = /*@__PURE__*/
  messageDesc(file_db, 11);

/**
 * map provider usage statistics
 *
 * @generated from message dbproto.DbMapProviderTokenUsage
 */
export type DbMapProviderTokenUsage = Message<"dbproto.DbMapProviderTokenUsage"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * provider rid
   *
   * @generated from field: string ProviderRid = 2;
   */
  ProviderRid: string;

  /**
   * @generated from field: int64 StartTime = 3;
   */
  StartTime: bigint;

  /**
   * rotate time
   *
   * @generated from field: int64 RotateTime = 4;
   */
  RotateTime: bigint;

  /**
   * usage count, request success times
   *
   * @generated from field: int32 UsageCount = 5;
   */
  UsageCount: number;
};

/**
 * Describes the message dbproto.DbMapProviderTokenUsage.
 * Use `create(DbMapProviderTokenUsageSchema)` to create a new message.
 */
export const DbMapProviderTokenUsageSchema: GenMessage<DbMapProviderTokenUsage> = /*@__PURE__*/
  messageDesc(file_db, 12);

/**
 * map cache roadmap index
 *
 * @generated from message dbproto.DbMapCacheRoadmapIndex
 */
export type DbMapCacheRoadmapIndex = Message<"dbproto.DbMapCacheRoadmapIndex"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: dbproto.MapProviderEnum Provider = 2;
   */
  Provider: MapProviderEnum;

  /**
   * language
   *
   * @generated from field: string Language = 3;
   */
  Language: string;

  /**
   * tile x
   *
   * @generated from field: int32 TileX = 4;
   */
  TileX: number;

  /**
   * tile y
   *
   * @generated from field: int32 TileY = 5;
   */
  TileY: number;

  /**
   * tile z
   *
   * @generated from field: int32 TileZ = 6;
   */
  TileZ: number;

  /**
   * cache time
   *
   * @generated from field: int64 CacheTime = 7;
   */
  CacheTime: bigint;

  /**
   * @generated from field: int64 AccessTime = 8;
   */
  AccessTime: bigint;

  /**
   * tile image type 1:png 2:jpg
   *
   * @generated from field: int32 TileImageFormat = 9;
   */
  TileImageFormat: number;

  /**
   * 128 bit xxHash hash, base64=key in kvstore
   *
   * @generated from field: string TileHash = 10;
   */
  TileHash: string;

  /**
   * @generated from field: int32 Gcj02 = 11;
   */
  Gcj02: number;

  /**
   * status, 1:active 8:deleted
   *
   * @generated from field: int32 Status = 12;
   */
  Status: number;
};

/**
 * Describes the message dbproto.DbMapCacheRoadmapIndex.
 * Use `create(DbMapCacheRoadmapIndexSchema)` to create a new message.
 */
export const DbMapCacheRoadmapIndexSchema: GenMessage<DbMapCacheRoadmapIndex> = /*@__PURE__*/
  messageDesc(file_db, 13);

/**
 * map cache satellite index
 *
 * @generated from message dbproto.DbMapCacheSatelliteIndex
 */
export type DbMapCacheSatelliteIndex = Message<"dbproto.DbMapCacheSatelliteIndex"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: dbproto.MapProviderEnum Provider = 2;
   */
  Provider: MapProviderEnum;

  /**
   * tile x
   *
   * @generated from field: int32 TileX = 3;
   */
  TileX: number;

  /**
   * tile y
   *
   * @generated from field: int32 TileY = 4;
   */
  TileY: number;

  /**
   * tile z
   *
   * @generated from field: int32 TileZ = 5;
   */
  TileZ: number;

  /**
   * cache time
   *
   * @generated from field: int64 CacheTime = 6;
   */
  CacheTime: bigint;

  /**
   * @generated from field: int64 AccessTime = 7;
   */
  AccessTime: bigint;

  /**
   * tile image type 1:png 2:jpg
   *
   * @generated from field: int32 TileImageFormat = 8;
   */
  TileImageFormat: number;

  /**
   * 128 bit xxHash hash, base64=key in kvstore
   *
   * @generated from field: string TileHash = 9;
   */
  TileHash: string;

  /**
   * @generated from field: int32 Gcj02 = 10;
   */
  Gcj02: number;

  /**
   * status, 1:active 8:deleted
   *
   * @generated from field: int32 Status = 11;
   */
  Status: number;
};

/**
 * Describes the message dbproto.DbMapCacheSatelliteIndex.
 * Use `create(DbMapCacheSatelliteIndexSchema)` to create a new message.
 */
export const DbMapCacheSatelliteIndexSchema: GenMessage<DbMapCacheSatelliteIndex> = /*@__PURE__*/
  messageDesc(file_db, 14);

/**
 * map cache hybrid index
 *
 * @generated from message dbproto.DbMapCacheHybridIndex
 */
export type DbMapCacheHybridIndex = Message<"dbproto.DbMapCacheHybridIndex"> & {
  /**
   * rid uuidv7
   *
   * @generated from field: string Rid = 1;
   */
  Rid: string;

  /**
   * @generated from field: dbproto.MapProviderEnum Provider = 2;
   */
  Provider: MapProviderEnum;

  /**
   * language
   *
   * @generated from field: string Language = 3;
   */
  Language: string;

  /**
   * tile x
   *
   * @generated from field: int32 TileX = 4;
   */
  TileX: number;

  /**
   * tile y
   *
   * @generated from field: int32 TileY = 5;
   */
  TileY: number;

  /**
   * tile z
   *
   * @generated from field: int32 TileZ = 6;
   */
  TileZ: number;

  /**
   * cache time
   *
   * @generated from field: int64 CacheTime = 7;
   */
  CacheTime: bigint;

  /**
   * @generated from field: int64 AccessTime = 8;
   */
  AccessTime: bigint;

  /**
   * tile image type 1:png 2:jpg
   *
   * @generated from field: int32 TileImageFormat = 9;
   */
  TileImageFormat: number;

  /**
   * 128 bit xxHash hash, base64=key in kvstore
   *
   * @generated from field: string TileHash = 10;
   */
  TileHash: string;

  /**
   * @generated from field: int32 Gcj02 = 11;
   */
  Gcj02: number;

  /**
   * status, 1:active 8:deleted
   *
   * @generated from field: int32 Status = 12;
   */
  Status: number;
};

/**
 * Describes the message dbproto.DbMapCacheHybridIndex.
 * Use `create(DbMapCacheHybridIndexSchema)` to create a new message.
 */
export const DbMapCacheHybridIndexSchema: GenMessage<DbMapCacheHybridIndex> = /*@__PURE__*/
  messageDesc(file_db, 15);

/**
 * @generated from enum dbproto.MapProviderEnum
 */
export enum MapProviderEnum {
  /**
   * @generated from enum value: ProviderGoogle = 0;
   */
  ProviderGoogle = 0,

  /**
   * @generated from enum value: ProviderTianditu = 1;
   */
  ProviderTianditu = 1,

  /**
   * @generated from enum value: ProviderOSM = 2;
   */
  ProviderOSM = 2,

  /**
   * @generated from enum value: ProviderGoogleLocalDirectory = 3;
   */
  ProviderGoogleLocalDirectory = 3,

  /**
   * @generated from enum value: ProviderTiandituLocalDirectory = 4;
   */
  ProviderTiandituLocalDirectory = 4,

  /**
   * @generated from enum value: ProviderOSMLocalDirectory = 5;
   */
  ProviderOSMLocalDirectory = 5,
}

/**
 * Describes the enum dbproto.MapProviderEnum.
 */
export const MapProviderEnumSchema: GenEnum<MapProviderEnum> = /*@__PURE__*/
  enumDesc(file_db, 0);

/**
 * @generated from enum dbproto.GoogleMapApiEnum
 */
export enum GoogleMapApiEnum {
  /**
   * @generated from enum value: GoogleMapApiStatic = 0;
   */
  GoogleMapApiStatic = 0,

  /**
   * @generated from enum value: GoogleMapApiTile = 1;
   */
  GoogleMapApiTile = 1,

  /**
   * @generated from enum value: GoogleMapApiAll = 2;
   */
  GoogleMapApiAll = 2,
}

/**
 * Describes the enum dbproto.GoogleMapApiEnum.
 */
export const GoogleMapApiEnumSchema: GenEnum<GoogleMapApiEnum> = /*@__PURE__*/
  enumDesc(file_db, 1);

