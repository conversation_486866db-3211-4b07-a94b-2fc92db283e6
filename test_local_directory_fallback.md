# 本地文件夹Fallback机制实现测试

## 实现完成总结

✅ **已完成的功能模块**：

### 1. Provider映射函数

- `getLocalDirectoryProviderType()`: 将在线provider映射到本地文件夹provider
- 映射关系：Google(0)→GoogleLocalDirectory(3), Tianditu(1)→TiandituLocalDirectory(4), OSM(2)→OSMLocalDirectory(5)

### 2. 本地文件读取方法（重构后）

- `QueryTileFromLocalDirectory()`: 从本地文件系统读取瓦片的主要方法
- `readLocalTileFile()`: 底层文件读取函数
- `reqMapTileFromLocalDirectory()`: 配合ReqMapFromProviderWithToken使用的方法
- `readTileFromLocalDirectoryWithToken()`: **新增共享逻辑函数**，减少代码重复

### 3. 主流程集成

- 在`MapHttpHandler`的`sfg.Do`函数中正确插入了本地文件夹fallback逻辑
- 执行顺序：缓存查询失败/需要更新 → **本地文件夹fallback** → NATS请求 → 在线provider请求

### 4. ReqMapFromProviderWithToken扩展

- 添加了对本地文件夹provider类型的支持
- 支持ProviderGoogleLocalDirectory、ProviderTiandituLocalDirectory、ProviderOSMLocalDirectory

### 5. 扩展应用场景（重构后）

- **handleWithCacheOrOSMMapTile方法重构**：添加`enableLocalDirectoryFallback`参数控制本地文件夹fallback
  - **启用场景**：`sysexpired=1`过期token请求 - `handleWithCacheOrOSMMapTile(w, r, mapReq, true)`
  - **禁用场景**：临时token请求 - `handleWithCacheOrOSMMapTile(w, r, mapReq, false)`
- **natsHandlerForMapReq方法**：为NATS节点间瓦片请求添加本地文件夹fallback
- 精确控制本地文件夹fallback的启用场景，避免不必要的文件系统访问

### 6. 文件路径格式

- 实现了需求文档中定义的路径格式：`{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}`
- 支持png和jpg格式的自动识别

## 核心实现特点

### ✅ 正确的执行顺序

按照需求文档要求，本地文件夹fallback在NATS请求**之前**执行：

```
缓存查询 → 本地文件夹fallback → NATS请求 → 在线provider请求
```

### ✅ 错误处理完善

- 本地文件夹fallback失败时，继续执行现有的NATS和在线provider流程
- 不影响原有逻辑，保持系统稳定性
- 详细的日志记录，便于调试

### ✅ 性能优化

- 使用`filepath.Join()`构建跨平台兼容的文件路径
- 文件存在性检查避免不必要的读取操作
- 支持多个token的fallback机制

### ✅ 配置灵活性

- 支持BaseUrl配置本地目录根路径
- 自动根据mapType和imageFormat确定文件扩展名
- 兼容现有的token管理机制

### ✅ 代码重构优化

- **消除重复逻辑**：将`reqMapTileFromLocalDirectory`和`QueryTileFromLocalDirectory`中的重复代码提取为共享函数
- **新增共享函数**：`readTileFromLocalDirectoryWithToken()`统一处理文件路径构建和读取逻辑
- **提高可维护性**：减少代码重复，降低维护成本，提高代码可读性
- **保持一致性**：确保两个方法使用相同的文件路径格式和错误处理逻辑

### ✅ 精确控制重构

- **方法签名重构**：`handleWithCacheOrOSMMapTile`添加`enableLocalDirectoryFallback bool`参数
- **条件控制**：通过参数精确控制何时启用本地文件夹fallback
- **场景区分**：
  - 过期token场景（`sysexpired=1`）：启用本地文件夹fallback
  - 临时token场景：禁用本地文件夹fallback，避免不必要的文件系统访问

### ✅ 语言支持扩展（新增）

- **数据库字段扩展**：为`DbMapProviderToken`添加`Language`字段（字段20）
- **Go结构体扩展**：在`MapProviderToken`中添加`Language string`字段
- **语言匹配逻辑**：实现`isLanguageMatched`函数，支持智能语言匹配
  - roadmap/hybrid类型：严格匹配token.Language与mapReq.Lang
  - satellite类型：跳过语言匹配（卫星图无语言差别）
  - 空Language字段：作为默认语言，始终匹配（向后兼容）
- **多语言瓦片支持**：通过不同BaseUrl路径存储不同语言版本的瓦片

## 测试建议

### 1. 单元测试

```bash
# 测试provider映射
getLocalDirectoryProvider(0) == 3  # Google
getLocalDirectoryProvider(1) == 4  # Tianditu
getLocalDirectoryProvider(2) == 5  # OSM

# 测试文件路径构建
BaseUrl="/data/tiles" + mapType="satellite" + z=10 + x=512 + y=256 + ext="jpg"
=> "/data/tiles/satellite/10/512/256.jpg"
```

### 2. 集成测试

1. 创建测试用的本地文件夹结构
2. 配置本地文件夹provider token（BaseUrl指向测试目录）
3. 测试各种场景下的fallback机制

### 3. 功能验证

- 缓存失效时是否正确触发本地文件夹fallback
- 本地文件不存在时是否正确fallback到NATS/在线provider
- 不同mapType和imageFormat的文件路径是否正确

## 部署注意事项

1. **本地文件夹权限**：确保应用有读取本地文件夹的权限
2. **BaseUrl配置**：在DbMapProviderToken中正确配置BaseUrl字段
3. **文件格式**：确保本地文件使用正确的扩展名（png/jpg）
4. **监控日志**：关注本地文件夹fallback的成功率和性能

## 实现符合需求

✅ 完全按照`docs/requirements/REQUIREMENTS_local_directory_fallback.md`需求文档实现
✅ 正确的执行顺序（本地文件夹fallback在NATS之前）
✅ 完善的错误处理（不影响现有流程）
✅ 标准的文件路径格式
✅ 支持所有三种provider类型的映射
